2025-07-01 19:00:39 - Main - INFO - Starting Server
2025-07-01 19:00:39 - Main - INFO - Connection at: **************:9092
2025-07-01 19:00:39 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 19:00:39 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-01 19:00:39 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 19:00:39 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 19:00:39 - Red<PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 19:00:41 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 19:00:41 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
